/* Fresh mobile-first UI */
:root {
  --bg: #f7f9fc;         /* light background */
  --text: #0f172a;       /* near-black */
  --muted: #64748b;      /* slate-500 */
  --card: #ffffff;       /* white card */
  --card2: #ffffff;      /* topbar */
  --brand: #2563eb;      /* blue-600 */
  --accent: #22c55e;     /* emerald-500 */
  --line: #e5e7eb;       /* light border */
  --danger: #ef4444;     /* red-500 */
}

html, body { width: 100%; max-width: 100%; overflow-x: hidden; background: var(--bg); color: var(--text); }
* , *::before, *::after { box-sizing: border-box; }

.App { color: var(--text); background: var(--bg); min-height: 100vh; padding-bottom: 72px; }

.Topbar {
  height: 56px; display: flex; align-items: center; justify-content: space-between;
  padding: 0 14px; border-bottom: 1px solid var(--line); background: var(--card2);
}
.Brand { display: flex; align-items: center; gap: 10px; font-weight: 800; }
.Brand .Logo { width: 28px; height: 28px; border-radius: 7px; background: linear-gradient(135deg,#22c55e,#60a5fa); }
.TopIcons { display: flex; align-items: center; gap: 14px; color: var(--muted); }

.Container { padding: 12px; display: flex; flex-direction: column; gap: 12px; }
.Card { background: var(--card); border: 1px solid var(--line); border-radius: 12px; padding: 12px; }
.Title { font-weight: 700; margin-bottom: 6px; }
.Micro { color: var(--muted); font-size: 12px; }
.Row { display: flex; align-items: center; gap: 10px; }

/* Composer */
.Composer { display: flex; flex-direction: column; gap: 8px; }
.InputFake { border: 1px solid var(--line); border-radius: 10px; padding: 10px 12px; color: var(--muted); background: #0b1220; }
.Pill { border: 1px solid var(--line); padding: 6px 10px; border-radius: 999px; color: var(--muted); }
.Primary { background: var(--brand); color: #06210f; padding: 8px 14px; border-radius: 10px; font-weight: 800; }
.Spacer { flex: 1; }

/* Post */
.Post { display: flex; flex-direction: column; gap: 8px; }
.PostHeader { display: flex; align-items: center; gap: 10px; }
.Avatar { width: 36px; height: 36px; border-radius: 50%; background: linear-gradient(135deg,#60a5fa,#22c55e); }
.PostBody { line-height: 1.5; }
.PostActions { display: flex; align-items: center; gap: 14px; color: var(--muted); }
.Action { padding: 6px 10px; border: 1px solid var(--line); border-radius: 8px; }
.Action.active { color: var(--brand); border-color: var(--brand); }

/* Chips grid */
.Chips { display: flex; flex-wrap: wrap; gap: 8px; }
.Chip { padding: 6px 10px; border-radius: 999px; background: #0b1220; border: 1px solid var(--line); color: var(--muted); }

/* Bottom nav */
.BottomNav { position: fixed; left: 0; right: 0; bottom: 0; height: 60px; background: var(--card2); border-top: 1px solid var(--line); display: flex; align-items: center; justify-content: space-around; padding-bottom: env(safe-area-inset-bottom, 0); }
.Tab { display: flex; flex-direction: column; align-items: center; gap: 2px; color: var(--muted); }
.Tab.active { color: var(--brand); }

/* Responsive */
@media (min-width: 900px) {
  .App { max-width: 520px; margin: 0 auto; border-left: 1px solid var(--line); border-right: 1px solid var(--line); }
}


