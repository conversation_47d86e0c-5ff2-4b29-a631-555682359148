import { useEffect, useMemo, useState } from '@lynx-js/react'
// Emoji-based icons fallback (robust across Lynx)
const EMOJI: Record<string, string> = {
  search: '🔎', bell: '🔔', gear: '⚙️', home: '🏠', compass: '🧭', plus: '➕', user: '👤', heart: '❤️', message: '💬', share: '↗️'
} as const

type IconName = keyof typeof EMOJI

function Icon(props: { name: IconName; active?: boolean; size?: number }) {
  const size = props.size ?? 20
  const color = props.active ? '#2563eb' : '#64748b'
  return <text style={{ fontSize: `${size}px`, color }}>{EMOJI[props.name]}</text>
}

import './new-ui.css'

import * as DB from './db.js'

type Tab = 'feed' | 'explore' | 'create' | 'alerts' | 'profile'

type Post = { id: string; author: string; text: string; liked: boolean; likes: number }

export function App(props: { onRender?: () => void }) {
  const [tab, setTab] = useState<Tab>('feed')
  const [posts, setPosts] = useState<Post[]>([])

  useEffect(() => { console.info('Freedom Wall — fresh mobile UI') }, [])
  props.onRender?.()
  // Load from SQLite on mount
  useEffect(() => {
    (async () => {
      try {
        const rows = await DB.listPosts()
        setPosts(rows.map((r: DB.PostRow) => ({ id: String(r.id), author: r.author, text: r.text, liked: !!r.liked, likes: r.likes })))
      } catch (e) {
        console.warn('DB load failed', e)
      }
    })()
  }, [])

  async function handleLike(id: string) {
    try {
      await DB.toggleLike(Number(id))
    } catch {}
    setPosts(prev => prev.map(x => x.id === id ? { ...x, liked: !x.liked, likes: x.liked ? Math.max(0, x.likes - 1) : x.likes + 1 } : x))
    setPopId(id)
    setTimeout(() => setPopId(null), 220)
  }

  async function handleCreate() {
    const text = 'New thought • ' + new Date().toLocaleTimeString()
    const author = 'You'
    try {
      const id = await DB.addPost({ author, text })
      setPosts(prev => [{ id: String(id), author, text, liked: false, likes: 0 }, ...prev])
      setTab('feed')
    } catch (e) {
      console.warn('Create post failed', e)
    }
  }

  const [popId, setPopId] = useState<string | null>(null)

  const feed = useMemo(() => (
    <>
      <view className='Card Composer'>
        <view className='Row'>
          <view className='Avatar' />
          <view className='InputFake'><text>Share a thought…</text></view>
        </view>
        <view className='Row'>
          <view className='Pill'><text>Mood ▾</text></view>
          <view className='Pill'><text>Category ▾</text></view>
          <view className='Spacer' />
          <view className='Primary' bindtap={handleCreate}><text>Post</text></view>
        </view>
      </view>

      {posts.map(p => (
        <view key={p.id} className='Card Post'>
          <view className='PostHeader'>
            <view className='Avatar' />

            <view>
              <text>{p.author}</text>
              <view><text className='Micro'>just now</text></view>
            </view>
          </view>
          <view className='PostBody'><text>{p.text}</text></view>
          <view className='PostActions'>
            <view
              className={`Action ${p.liked ? 'active' : ''} ${popId === p.id ? 'LikePop' : ''}`}
              bindtap={() => handleLike(p.id)}
            >
              <Icon name='heart' active={p.liked} />
              <text>{p.likes}</text>
            </view>
            <view className='Action'><Icon name='message' /></view>
            <view className='Action'><Icon name='share' /></view>
          </view>
        </view>
      ))}

      <view className='Card'>
        <text className='Title'>Topics for you</text>
        <view className='Chips'>
          <view className='Chip'><text>#StudyTips</text></view>
          <view className='Chip'><text>#Motivation</text></view>
          <view className='Chip'><text>#MentalHealth</text></view>
          <view className='Chip'><text>#Confessions</text></view>
        </view>
      </view>
    </>
  ), [posts, popId])

  const explore = (
    <view className='Card'>
      <text className='Title'>Explore</text>
      <view className='Chips'>
        <view className='Chip'><text>Academics</text></view>
        <view className='Chip'><text>Relationships</text></view>
        <view className='Chip'><text>Confessions</text></view>
        <view className='Chip'><text>Support</text></view>
      </view>
    </view>
  )

  const create = (
    <view className='Card Composer'>
      <text className='Title'>Create Post</text>
      <view className='InputFake'><text>Write something inspiring…</text></view>
      <view className='Row'>
        <view className='Pill'><text>Anonymous: On</text></view>
        <view className='Spacer' />
        <view className='Primary'><text>Publish</text></view>
      </view>
    </view>
  )

  const alerts = (
    <view className='Card'>
      <text className='Title'>Alerts</text>
      <view className='Row'><text>🔔</text><text>Someone liked your post</text></view>
      <view className='Row'><text>🔔</text><text>New comment on your question</text></view>
    </view>
  )

  const profile = (
    <view className='Card'>
      <text className='Title'>Profile</text>
      <view className='Row'>
        <view className='Avatar' />
        <view>
          <text>You</text>
          <view><text className='Micro'>Member since 2025</text></view>
        </view>
      </view>
    </view>
  )

  return (
    <view className='App'>
      <view className='Topbar'>
        <view className='Brand'><view className='Logo' /><text>Freedom Wall</text></view>
        <view className='TopIcons'><Icon name='search' /><Icon name='bell' /><Icon name='gear' /></view>
      </view>

      <view className='Container'>
        {tab === 'feed' && feed}
        {tab === 'explore' && explore}
        {tab === 'create' && create}
        {tab === 'alerts' && alerts}
        {tab === 'profile' && profile}
      </view>

      <view className='BottomNav'>
        <view className={`Tab ${tab === 'feed' ? 'active' : ''}`} bindtap={() => setTab('feed')}><Icon name='home' active={tab==='feed'} /><text className='Micro'>Feed</text></view>
        <view className={`Tab ${tab === 'explore' ? 'active' : ''}`} bindtap={() => setTab('explore')}><Icon name='compass' active={tab==='explore'} /><text className='Micro'>Explore</text></view>
        <view className={`Tab ${tab === 'create' ? 'active' : ''}`} bindtap={() => setTab('create')}><Icon name='plus' active={tab==='create'} /><text className='Micro'>Create</text></view>
        <view className={`Tab ${tab === 'alerts' ? 'active' : ''}`} bindtap={() => setTab('alerts')}><Icon name='bell' active={tab==='alerts'} /><text className='Micro'>Alerts</text></view>
        <view className={`Tab ${tab === 'profile' ? 'active' : ''}`} bindtap={() => setTab('profile')}><Icon name='user' active={tab==='profile'} /><text className='Micro'>Profile</text></view>
      </view>
    </view>
  )
}
