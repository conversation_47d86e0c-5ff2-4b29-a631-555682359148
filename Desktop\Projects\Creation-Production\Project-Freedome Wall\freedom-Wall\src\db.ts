import initSqlJs, { type Database, type SqlJsStatic } from 'sql.js'

// Local storage key for persisted DB bytes
const LS_KEY = 'fw.sqlite.v1'
let sqlPromise: Promise<SqlJsStatic> | null = null
let dbPromise: Promise<Database> | null = null

function bytesToBase64(bytes: Uint8Array): string {
  let binary = ''
  const len = bytes.byteLength
  for (let i = 0; i < len; i++) binary += String.fromCharCode(bytes[i])
  return btoa(binary)
}
function base64ToBytes(base64: string): Uint8Array {
  const binary = atob(base64)
  const len = binary.length
  const bytes = new Uint8Array(len)
  for (let i = 0; i < len; i++) bytes[i] = binary.charCodeAt(i)
  return bytes
}

async function loadSql(): Promise<SqlJsStatic> {
  if (!sqlPromise) {
    // Try to let bundler resolve the wasm file via URL from the package
    let locateFile: (file: string) => string
    try {
      // Rspack/new URL resolution at runtime
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const wasmUrl = new URL('sql.js/dist/sql-wasm.wasm', import.meta.url).toString()
      locateFile = (file) => (file.endsWith('.wasm') ? wasmUrl : file)
    } catch {
      locateFile = (file) => file
    }
    sqlPromise = initSqlJs({ locateFile })
  }
  return sqlPromise
}

async function openDb(): Promise<Database> {
  if (!dbPromise) {
    dbPromise = (async () => {
      const SQL = await loadSql()
      const saved = localStorage.getItem(LS_KEY)
      const db = saved ? new SQL.Database(base64ToBytes(saved)) : new SQL.Database()
      // Init schema
      db.exec(`CREATE TABLE IF NOT EXISTS posts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        author TEXT NOT NULL,
        text TEXT NOT NULL,
        category TEXT,
        mood TEXT,
        created_at INTEGER NOT NULL,
        likes INTEGER NOT NULL DEFAULT 0,
        liked INTEGER NOT NULL DEFAULT 0
      );`)
      return db
    })()
  }
  return dbPromise
}

async function persist(db: Database) {
  const bytes = db.export()
  const b64 = bytesToBase64(bytes)
  localStorage.setItem(LS_KEY, b64)
}

export type PostRow = {
  id: number
  author: string
  text: string
  category: string | null
  mood: string | null
  created_at: number
  likes: number
  liked: number
}

export async function listPosts(): Promise<PostRow[]> {
  const db = await openDb()
  const stmt = db.prepare('SELECT id, author, text, category, mood, created_at, likes, liked FROM posts ORDER BY created_at DESC')
  const rows: PostRow[] = []
  while (stmt.step()) rows.push(stmt.getAsObject() as unknown as PostRow)
  stmt.free()
  return rows
}

export async function addPost(post: { author: string; text: string; category?: string | null; mood?: string | null }): Promise<number> {
  const db = await openDb()
  const now = Date.now()
  const stmt = db.prepare('INSERT INTO posts(author, text, category, mood, created_at, likes, liked) VALUES (?, ?, ?, ?, ?, 0, 0)')
  stmt.bind([post.author, post.text, post.category ?? null, post.mood ?? null, now])
  stmt.step()
  stmt.free()
  await persist(db)
  // get last inserted id
  const idStmt = db.prepare('SELECT last_insert_rowid() as id')
  idStmt.step()
  const { id } = idStmt.getAsObject() as { id: number }
  idStmt.free()
  return id
}

export async function toggleLike(id: number): Promise<void> {
  const db = await openDb()
  const get = db.prepare('SELECT liked, likes FROM posts WHERE id = ?')
  get.bind([id])
  if (!get.step()) {
    get.free(); return
  }
  const row = get.getAsObject() as { liked: number; likes: number }
  get.free()
  const liked = row.liked ? 0 : 1
  const likes = row.liked ? Math.max(0, row.likes - 1) : row.likes + 1
  const upd = db.prepare('UPDATE posts SET liked = ?, likes = ? WHERE id = ?')
  upd.bind([liked, likes, id])
  upd.step()
  upd.free()
  await persist(db)
}

export async function clearAll(): Promise<void> {
  const db = await openDb()
  db.exec('DELETE FROM posts; VACUUM;')
  await persist(db)
}

